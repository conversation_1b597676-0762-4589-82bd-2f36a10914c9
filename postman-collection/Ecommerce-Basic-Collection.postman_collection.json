{"info": {"_postman_id": "ecom-basic-collection", "name": "Ecommerce Basic API Collection", "description": "Simplified collection with essential endpoints only", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Test123!\",\n  \"first_name\": \"Test\",\n  \"last_name\": \"User\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    pm.test('Registration successful', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        if (responseJson.data && responseJson.data.id) {", "            pm.environment.set('user_id', responseJson.data.id);", "            pm.environment.set('user_email', responseJson.data.email);", "            console.log('✅ User registered:', responseJson.data.email);", "        }", "    });", "} else {", "    pm.test('Registration failed', function () {", "        console.log('❌ Status:', pm.response.code);", "        console.log('Response:', pm.response.text());", "    });", "}"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Test123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    pm.test('Login successful', function () {", "        const responseJson = pm.response.json();", "        if (responseJson.data && responseJson.data.token) {", "            pm.environment.set('jwt_token', responseJson.data.token);", "            pm.environment.set('user_id', responseJson.data.user.id);", "            console.log('✅ Login successful');", "        }", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        console.log('❌ Status:', pm.response.code);", "        console.log('Response:', pm.response.text());", "    });", "}"], "type": "text/javascript"}}]}]}, {"name": "👤 User Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Profile retrieved', function () {", "    if (pm.response.code === 200) {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        console.log('✅ Profile loaded');", "    } else {", "        console.log('❌ Status:', pm.response.code);", "        console.log('Response:', pm.response.text());", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "🛍️ Products", "item": [{"name": "Get Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Products retrieved', function () {", "    if (pm.response.code === 200) {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        console.log('✅ Products loaded');", "    } else {", "        console.log('❌ Status:', pm.response.code);", "        console.log('Response:', pm.response.text());", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "🔧 System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('System healthy', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    console.log('✅ System is healthy');", "});"], "type": "text/javascript"}}]}]}]}