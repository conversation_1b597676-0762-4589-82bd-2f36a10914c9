{"info": {"_postman_id": "ecom-golang-api-collection-auth", "name": "Ecommerce Golang Clean Architecture API - Auth Only", "description": "Authentication endpoints only for testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "description": "User authentication and authorization endpoints", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user account. All fields except phone are required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has correct structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.message).to.equal('User registered successfully');", "});"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"ip_address\": \"127.0.0.1\",\n  \"user_agent\": \"PostmanRuntime/7.32.3\",\n  \"device_info\": \"Postman API Client\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticate user and receive JWT tokens. Returns access token and refresh token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('object');", "});", "", "pm.test('Store authentication data in environment', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set('jwt_token', responseJson.data.token);", "        console.log('✅ JWT token stored');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user and invalidate current JWT token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}]}]}